<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barcode Printing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .barcode-preview-container {
            border: 2px solid #e3e6f0;
            padding: 15px;
            display: inline-block;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            background: white;
            transition: all 0.3s ease;
            margin: 10px auto;
        }

        .barcode-preview-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
        }

        .barcode-content {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .card {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 15px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .card-body {
            padding: 2rem;
        }

        .form-control, .form-select {
            border-radius: 8px;
            font-size: 0.875rem;
            padding: 0.75rem 1rem;
            border: 2px solid #e3e6f0;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }

        .btn {
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #4e73df, #224abe);
        }

        .btn-outline-primary {
            border: 2px solid #4e73df;
            color: #4e73df;
        }

        .btn-outline-primary:hover {
            background: #4e73df;
            color: white;
        }

        h2, h4 {
            background: linear-gradient(45deg, #4e73df, #224abe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }

        .form-check-input:checked {
            background-color: #4e73df;
            border-color: #4e73df;
        }

        .preview-section {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="mb-3" style="color: #5a5c69; font-weight: 500; font-size: 1.75rem;">Barcode Printing</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb" style="background-color: transparent; padding: 0; margin-bottom: 1.5rem;">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}" style="color: #4e73df; text-decoration: none;">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('medicines.index') }}" style="color: #4e73df; text-decoration: none;">Medicines</a></li>
                        <li class="breadcrumb-item active" style="color: #858796;">Barcode Printing</li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <!-- Printer Settings Section -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h4 style="color: #4e73df; font-size: 1.1rem; font-weight: 500; margin-bottom: 1rem;">Printer Settings</h4>
                        <div class="mb-3">
                            <label for="printer" class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Printer</label>
                            <select class="form-select" id="printer">
                                <option value="default">Default Printer</option>
                                <option value="ZDesinger GC420d (EPL)">ZDesinger GC420d (EPL)</option>
                                <option value="other">Other Printer</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="number_of_labels" class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Number of Labels</label>
                            <input type="number" class="form-control" id="number_of_labels" min="1" value="1">
                        </div>

                        <div class="mb-3">
                            <label for="code_type" class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Code Type</label>
                            <select class="form-select" id="code_type">
                                <option value="CODE128">Code-128 (Recommended)</option>
                                <option value="EAN13">EAN-13 (Numbers only)</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Size</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_30_20" value="30*20" checked>
                                <label class="form-check-label" for="size_30_20">30 * 20 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_40_15" value="40*15">
                                <label class="form-check-label" for="size_40_15">40 * 15 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_40_25" value="40*25">
                                <label class="form-check-label" for="size_40_25">40 * 25 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_50_25" value="50*25">
                                <label class="form-check-label" for="size_50_25">50 * 25 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_60_30" value="60*30">
                                <label class="form-check-label" for="size_60_30">60 * 30 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_70_40" value="70*40">
                                <label class="form-check-label" for="size_70_40">70 * 40 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_80_50" value="80*50">
                                <label class="form-check-label" for="size_80_50">80 * 50 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_90_70" value="90*70">
                                <label class="form-check-label" for="size_90_70">90 * 70 mm</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="label_size" id="size_a5_1" value="A5">
                                <label class="form-check-label" for="size_a5_1">A5</label>
                            </div>
                        </div>

                        <button type="button" class="btn btn-outline-primary" id="save_settings" style="border-radius: 4px; padding: 6px 12px; font-size: 14px;">
                            <i class="fa fa-save" style="margin-right: 5px;"></i> Save Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sample Preview Section -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h4 style="font-size: 1.2rem; margin-bottom: 1.5rem;">
                            <i class="fas fa-eye me-2"></i>Live Preview
                        </h4>
                        <div class="preview-section">
                            <div class="barcode-preview-container" id="preview_container">
                                <div class="barcode-content">
                                    <div id="preview_entity" style="font-weight: bold; font-size: 14px; color: #2c3e50; margin-bottom: 2px;">{{ auth()->user()->name ?? 'Sample Entity' }}</div>
                                    <div id="preview_mobile" style="font-size: 11px; color: #7f8c8d; margin-bottom: 4px;">Mobile: {{ auth()->user()->phone ?? '0750XXXXXXX' }}</div>
                                    <div id="preview_product_name" style="margin-bottom: 6px; font-size: 12px; color: #34495e; font-weight: 500;">{{ $medicine->name ?? 'Product Name' }}</div>
                                    <div id="preview_barcode_img" style="margin: 8px 0;">
                                        <svg id="barcode"></svg>
                                    </div>
                                    <div id="preview_barcode_text" style="font-size: 11px; color: #2c3e50; font-weight: 500;">{{ $medicine->qr_code ?? '123456789' }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Preview updates automatically as you type
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Fields Section -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <form id="barcode_print_form" method="GET">
                            <div class="mb-3">
                                <label for="entity" class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Entity</label>
                                <input type="text" class="form-control" id="entity" name="entity" value="{{ auth()->user()->name ?? '' }}">
                            </div>

                            <div class="mb-3">
                                <label for="product_name" class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Product Name</label>
                                <input type="text" class="form-control" id="product_name" name="product_name" value="{{ $medicine->name ?? '' }}">
                            </div>

                            <div class="mb-3">
                                <label for="product_barcode" class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Product Barcode</label>
                                <input type="text" class="form-control" id="product_barcode" name="product_barcode" value="{{ $medicine->qr_code ?? '' }}">
                            </div>

                            <div class="mb-3">
                                <label for="product_price" class="form-label" style="font-weight: 500; color: #5a5c69; font-size: 0.875rem;">Product Price</label>
                                <input type="text" class="form-control" id="product_price" name="product_price" value="{{ $medicine->price ?? '' }}">
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" id="print_preview" style="border-radius: 4px; padding: 6px 12px; font-size: 14px;">
                                    <i class="fa fa-eye" style="margin-right: 5px;"></i> Print Preview
                                </button>
                                <button type="button" class="btn btn-primary" id="print_barcode" style="border-radius: 4px; padding: 6px 12px; font-size: 14px; background-color: #4e73df; border-color: #4e73df;">
                                    <i class="fa fa-print" style="margin-right: 5px;"></i> Print
                                </button>
                                <button type="button" class="btn btn-outline-danger" id="close_form" style="border-radius: 4px; padding: 6px 12px; font-size: 14px;">
                                    <i class="fa fa-times" style="margin-right: 5px;"></i> Close
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        $(document).ready(function() {
            // Configure toastr
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": false,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "3000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            };

            // Get size dimensions based on selected size
            function getSizeDimensions() {
                var selectedSize = $('input[name="label_size"]:checked').val();
                var dimensions = {
                    width: 2, height: 1, // Default in inches
                    barcodeWidth: 2, barcodeHeight: 50
                };

                switch(selectedSize) {
                    case '30*20':
                        dimensions = {width: 1.18, height: 0.79, barcodeWidth: 1.5, barcodeHeight: 35};
                        break;
                    case '40*15':
                        dimensions = {width: 1.57, height: 0.59, barcodeWidth: 1.8, barcodeHeight: 30};
                        break;
                    case '40*25':
                        dimensions = {width: 1.57, height: 0.98, barcodeWidth: 1.8, barcodeHeight: 40};
                        break;
                    case '50*25':
                        dimensions = {width: 1.97, height: 0.98, barcodeWidth: 2.2, barcodeHeight: 45};
                        break;
                    case '60*30':
                        dimensions = {width: 2.36, height: 1.18, barcodeWidth: 2.5, barcodeHeight: 55};
                        break;
                    case '70*40':
                        dimensions = {width: 2.76, height: 1.57, barcodeWidth: 2.8, barcodeHeight: 65};
                        break;
                    case '80*50':
                        dimensions = {width: 3.15, height: 1.97, barcodeWidth: 3.0, barcodeHeight: 75};
                        break;
                    case '90*70':
                        dimensions = {width: 3.54, height: 2.76, barcodeWidth: 3.2, barcodeHeight: 85};
                        break;
                    case 'A5':
                        dimensions = {width: 5.83, height: 8.27, barcodeWidth: 3.5, barcodeHeight: 90};
                        break;
                }
                return dimensions;
            }

            // Format barcode to optimal length
            function formatBarcodeValue(value) {
                // If using EAN13, ensure it's exactly 13 digits
                if ($('#code_type').val() === 'EAN13') {
                    // If numeric only, pad or truncate to 12 digits (13th is check digit)
                    if (/^\d+$/.test(value)) {
                        // Pad with zeros if less than 12 digits
                        while (value.length < 12) {
                            value = '0' + value;
                        }
                        // Truncate if more than 12 digits
                        if (value.length > 12) {
                            value = value.substring(0, 12);
                        }
                        return value;
                    } else {
                        // If not numeric, switch to CODE128
                        $('#code_type').val('CODE128');
                        toastr.warning('EAN13 requires numeric values only. Switched to CODE128.');
                    }
                }

                // For other formats, limit to 13 chars for better display
                if (value.length > 13) {
                    return value.substring(0, 13);
                }

                return value;
            }

            // Initialize barcode with selected format and dynamic sizing
            function generateCode() {
                var codeValue = $('#product_barcode').val() || '123456789';
                var format = $('#code_type').val();
                var dimensions = getSizeDimensions();

                // Format barcode value for optimal display
                codeValue = formatBarcodeValue(codeValue);

                // Update the input field with the formatted value
                if ($('#product_barcode').val() !== codeValue) {
                    $('#product_barcode').val(codeValue);
                    $('#preview_barcode_text').text(codeValue);
                }

                // Update preview container size
                $('#preview_container').css({
                    'width': dimensions.width + 'in',
                    'height': dimensions.height + 'in'
                });

                // Generate barcode with selected format and dynamic sizing
                try {
                    JsBarcode("#barcode", codeValue, {
                        format: format,
                        lineColor: "#000",
                        width: dimensions.barcodeWidth,
                        height: dimensions.barcodeHeight,
                        displayValue: false,
                        margin: 8,
                        background: "transparent"
                    });
                } catch (error) {
                    console.error('Barcode generation error:', error);
                    toastr.error('Error generating barcode. Please check the barcode value.');
                }
            }

            // Update preview size when size selection changes
            function updatePreviewSize() {
                generateCode();
                toastr.info('Preview size updated');
            }
            // Generate initial code
            generateCode();

            // Update preview when form fields change
            $('#entity').on('input', function() {
                $('#preview_entity').text($(this).val());
            });

            $('#product_name').on('input', function() {
                $('#preview_product_name').text($(this).val());
            });

            $('#product_barcode').on('input', function() {
                var code = $(this).val();
                $('#preview_barcode_text').text(code);
                generateCode();
            });

            // Handle code type change
            $('#code_type').on('change', function() {
                generateCode();
            });

            // Save settings button
            $('#save_settings').on('click', function() {
                // Save settings to localStorage
                var settings = {
                    printer: $('#printer').val(),
                    numberOfLabels: $('#number_of_labels').val(),
                    labelSize: $('input[name="label_size"]:checked').val(),
                    codeType: $('#code_type').val()
                };
                localStorage.setItem('barcodeSettings', JSON.stringify(settings));

                // Show success message
                toastr.success('Printer settings saved successfully');
            });

            // Print preview button
            $('#print_preview').on('click', function() {
                var printWindow = window.open('', '_blank');
                var entity = $('#entity').val();
                var mobile = $('#preview_mobile').text();
                var productName = $('#product_name').val();
                var productBarcode = $('#product_barcode').val();

                var content = '<html>' +
                    '<head>' +
                    '<title>Barcode Print Preview</title>' +
                    '<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"><\/script>' +
                    '<style>' +
                    'body { font-family: Arial, sans-serif; }' +
                    '.barcode-container { border: 1px solid #ccc; display: inline-block; margin: 10px; padding: 10px; }' +
                    '.barcode-content { background-color: #f8f9fa; padding: 15px; text-align: center; }' +
                    '.entity { font-weight: bold; font-size: 16px; }' +
                    '.product-name { margin-bottom: 5px; }' +
                    '</style>' +
                    '</head>' +
                    '<body>';

                var numberOfLabels = $('#number_of_labels').val();
                for (var i = 0; i < numberOfLabels; i++) {
                    content += '<div class="barcode-container">' +
                        '<div class="barcode-content">' +
                        '<div class="entity">' + entity + '</div>' +
                        '<div class="mobile">' + mobile + '</div>' +
                        '<div class="product-name">' + productName + '</div>' +
                        '<div class="barcode-img"><svg class="print-barcode"></svg></div>' +
                        '<div class="barcode-text">' + productBarcode + '</div>' +
                        '</div>' +
                        '</div>';

                    // Add line break after every 3 barcodes
                    if ((i + 1) % 3 === 0) {
                        content += '<br>';
                    }
                }

                content += '</body></html>';

                printWindow.document.open();
                printWindow.document.write(content);
                printWindow.document.close();

                // Generate barcodes in the preview window after content is loaded
                printWindow.onload = function() {
                    var barcodeValue = productBarcode || '123456789';
                    // Format barcode value for optimal display
                    barcodeValue = formatBarcodeValue(barcodeValue);

                    var barcodes = printWindow.document.querySelectorAll('.print-barcode');
                    barcodes.forEach(function(barcode) {
                        // Use the selected barcode format
                        var format = $('#code_type').val();

                        JsBarcode(barcode, barcodeValue, {
                            format: format,
                            lineColor: "#000",
                            width: 1.5,
                            height: 40,
                            displayValue: false,
                            margin: 5
                        });
                    });
                };
            });

            // Print button
            $('#print_barcode').on('click', function() {
                var printWindow = window.open('', '_blank');
                var entity = $('#entity').val();
                var mobile = $('#preview_mobile').text();
                var productName = $('#product_name').val();
                var productBarcode = $('#product_barcode').val();

                var content = '<html>' +
                    '<head>' +
                    '<title>Barcode Print</title>' +
                    '<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"><\/script>' +
                    '<style>' +
                    'body { font-family: Arial, sans-serif; }' +
                    '.barcode-container { border: 1px solid #ccc; display: inline-block; margin: 5px; padding: 5px; width: 2in; height: 1in; box-sizing: border-box; }' +
                    '.barcode-content { background-color: #f8f9fa; padding: 5px; text-align: center; height: 100%; display: flex; flex-direction: column; justify-content: center; }' +
                    '.entity { font-weight: bold; font-size: 12px; }' +
                    '.mobile { font-size: 10px; }' +
                    '.product-name { margin-bottom: 2px; font-size: 11px; }' +
                    '.barcode-text { font-size: 10px; }' +
                    '@media print { body { margin: 0; padding: 0; } .barcode-container { page-break-inside: avoid; } }' +
                    '</style>' +
                    '</head>' +
                    '<body>';

                var numberOfLabels = $('#number_of_labels').val();
                for (var i = 0; i < numberOfLabels; i++) {
                    content += '<div class="barcode-container" style="width: 2in; height: 1in; box-sizing: border-box; border: 1px solid #ccc; display: inline-block; margin: 5px; padding: 5px;">' +
                        '<div class="barcode-content" style="background-color: #f8f9fa; padding: 5px; text-align: center; height: 100%; display: flex; flex-direction: column; justify-content: center;">' +
                        '<div class="entity" style="font-weight: bold; font-size: 12px;">' + entity + '</div>' +
                        '<div class="mobile" style="font-size: 10px;">' + mobile + '</div>' +
                        '<div class="product-name" style="margin-bottom: 2px; font-size: 11px;">' + productName + '</div>' +
                        '<div class="barcode-img"><svg class="print-barcode"></svg></div>' +
                        '<div class="barcode-text" style="font-size: 10px;">' + productBarcode + '</div>' +
                        '</div>' +
                        '</div>';

                    // Add line break after every 3 barcodes
                    if ((i + 1) % 3 === 0) {
                        content += '<br>';
                    }
                }

                content += '</body></html>';

                printWindow.document.open();
                printWindow.document.write(content);
                printWindow.document.close();

                // Generate barcodes and trigger print after content is loaded
                printWindow.onload = function() {
                    var barcodeValue = productBarcode || '123456789';
                    // Format barcode value for optimal display
                    barcodeValue = formatBarcodeValue(barcodeValue);

                    var barcodes = printWindow.document.querySelectorAll('.print-barcode');
                    barcodes.forEach(function(barcode) {
                        // Use the selected barcode format
                        var format = $('#code_type').val();

                        JsBarcode(barcode, barcodeValue, {
                            format: format,
                            lineColor: "#000",
                            width: 1.5,
                            height: 40,
                            displayValue: false,
                            margin: 5
                        });
                    });

                    // Add a small delay to ensure barcodes are rendered before printing
                    setTimeout(function() {
                        printWindow.print();
                        // printWindow.close();
                    }, 500);
                };
            });

            // Close button
            $('#close_form').on('click', function() {
                window.close();
            });

            // Load saved settings if available
            var savedSettings = localStorage.getItem('barcodeSettings');
            if (savedSettings) {
                try {
                    var settings = JSON.parse(savedSettings);
                    $('#printer').val(settings.printer);
                    $('#number_of_labels').val(settings.numberOfLabels);
                    $('input[name="label_size"][value="' + settings.labelSize + '"]').prop('checked', true);
                    if (settings.codeType) {
                        $('#code_type').val(settings.codeType);
                        generateCode(); // Regenerate code with the saved type
                    }
                } catch (e) {
                    console.error('Error loading saved settings:', e);
                }
            }
        });
    </script>
</body>
</html>